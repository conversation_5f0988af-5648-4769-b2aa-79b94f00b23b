const admin = require('firebase-admin');
const { logger } = require('../app/utils/logger.utils');
const constantUtils = require('../app/utils/constants.utils');

const initializeFirebase = () => {
  try {
    if (admin.apps.length === 0) {
      const serviceAccount = {
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
      };

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID,
      });

      logger.info(constantUtils.FIREBASE_INITIALIZED);
    }
    return admin;
  } catch (error) {
    logger.error(`${constantUtils.FIREBASE_INITIALIZATION_FAILED} : ${error}`);
    throw error;
  }
};

module.exports = { initializeFirebase, admin };
