// dependencies
const admin = require('firebase-admin');

// models
const Notification = require('../models/notification.model');
const NotificationRecipient = require('../models/notification-recipient.model');
const Permission = require('../models/permission.model');
const AccountLicence = require('../models/account-licence.model');
const RoleAgreement = require('../models/role-agreement.model');
const User = require('../models/user.model');

// services
const notificationService = require('./notification.service');

// utils
const { logger } = require('../utils/logger.utils');
const constantUtils = require('../utils/constants.utils');

/**
 * Send single notification
 *
 * @param {Object} params - Notification parameters
 * @returns {Promise<Object>} Result object with success status and details
 */
exports.sendSingleNotification = async params => {
  try {
    const {
      title,
      body,
      module,
      priority = 'medium',
      actionUrl = null,
      accountId,
      senderId,
      recipient,
    } = params;

    // Validate required parameters
    if (!title || !body || !module || !accountId || !senderId || !recipient) {
      throw new Error(constantUtils.NOTIFICATION_MISSING_PARAMETERS);
    }

    // Create notification record
    const notification = await Notification.create({
      title,
      body,
      module,
      priority,
      actionUrl,
      account: accountId,
      sender: senderId,
      createdBy: senderId,
    });

    // Create notification recipient record
    await NotificationRecipient.create({
      notification: notification._id,
      recipient: recipient._id,
    });

    // Send push notifications to all FCM tokens of the recipient
    const pushResults = [];
    if (recipient.fcmTokens && recipient.fcmTokens.length > 0) {
      for (const fcmToken of recipient.fcmTokens) {
        try {
          const pushResult = await this.sendNotification(fcmToken, { title, body });
          pushResults.push({
            recipientId: recipient._id,
            fcmToken,
            success: true,
            result: pushResult,
          });
        } catch (pushError) {
          logger.error(constantUtils.NOTIFICATION_SENT_FAILED);
          pushResults.push({
            recipientId: recipient._id,
            fcmToken,
            success: false,
            error: pushError.message,
          });
        }
      }
    } else {
      pushResults.push({
        recipientId: recipient._id,
        fcmToken: null,
        success: false,
        error: 'No FCM tokens available',
      });
    }

    return {
      success: true,
      message: constantUtils.NOTIFICATION_SENT,
      notificationId: notification._id,
      recipientCount: 1,
      pushNotificationResults: pushResults,
    };
  } catch (error) {
    logger.error(constantUtils.NOTIFICATION_SENT_FAILED);
    throw error;
  }
};

/**
 * Send bulk notifications
 *
 * @param {Object} params - Notification parameters
 * @returns {Promise<Object>} Result object with success status and details
 */
exports.sendBulkNotifications = async params => {
  try {
    const {
      title,
      body,
      module,
      priority = 'medium',
      actionUrl = null,
      accountId,
      senderId,
      recipients,
    } = params;

    // Validate required parameters
    if (
      !title ||
      !body ||
      !module ||
      !accountId ||
      !senderId ||
      !recipients ||
      !Array.isArray(recipients)
    ) {
      throw new Error(constantUtils.NOTIFICATION_MISSING_PARAMETERS);
    }

    if (recipients.length === 0) {
      logger.warn(constantUtils.NO_RECIPIENTS_PROVIDED_FOR_NOTIFICATION);
      return {
        success: true,
        message: constantUtils.NO_RECIPIENTS_FOUND_FOR_NOTIFICATION,
        notificationId: null,
        recipientCount: 0,
        pushNotificationResults: [],
      };
    }

    // Create single notification record
    const notification = await Notification.create({
      title,
      body,
      module,
      priority,
      actionUrl,
      account: accountId,
      sender: senderId,
      createdBy: senderId,
    });

    // Create notification recipient records for all recipients
    const notificationRecipients = recipients.map(recipient => ({
      notification: notification._id,
      recipient: recipient._id,
    }));

    await NotificationRecipient.insertMany(notificationRecipients);

    // Send push notifications to all recipients
    const pushResults = [];

    for (const recipient of recipients) {
      if (recipient.fcmTokens && recipient.fcmTokens.length > 0) {
        for (const fcmToken of recipient.fcmTokens) {
          try {
            const pushResult = await this.sendNotification(fcmToken, { title, body });
            pushResults.push({
              recipientId: recipient._id,
              fcmToken,
              success: true,
              result: pushResult,
            });
          } catch (pushError) {
            logger.error(constantUtils.NOTIFICATION_SENT_FAILED);
            pushResults.push({
              recipientId: recipient._id,
              fcmToken,
              success: false,
              error: pushError.message,
            });
          }
        }
      } else {
        pushResults.push({
          recipientId: recipient._id,
          fcmToken: null,
          success: false,
          error: constantUtils.NO_FCM_TOKENS_AVAILABLE,
        });
      }
    }

    return {
      success: true,
      message: constantUtils.NOTIFICATION_SENT,
      notificationId: notification._id,
      recipientCount: recipients.length,
      pushNotificationResults: pushResults,
    };
  } catch (error) {
    logger.error(constantUtils.NOTIFICATION_SENT_FAILED);
    throw error;
  }
};

exports.sendNotification = async (fcmToken, notificationData) => {
  try {
    if (!fcmToken) {
      throw new Error(constantUtils.FCM_TOKEN_REQUIRED);
    }
    if (!notificationData?.title || !notificationData?.body) {
      throw new Error(constantUtils.NOTIFICATION_TITLE_AND_BODY_REQUIRED);
    }

    const payload = {
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      token: fcmToken,
    };

    const response = await admin.messaging().send(payload);
    if (response.failureCount > 0) {
      throw new Error(constantUtils.NOTIFICATION_SENT_FAILED);
    }

    return response;
  } catch (error) {
    logger.error(constantUtils.NOTIFICATION_SENT_FAILED);
  }
};

exports.getNotificationByFilter = async (filter, page, perPage) => {
  // Base query conditions
  const baseQuery = { recipient: filter.user, deletedAt: null };

  // Count total documents first
  const totalCountQuery = NotificationRecipient.find(baseQuery).populate({
    path: 'notification',
    match: { account: filter.account, deletedAt: null },
  });

  // Get total count (only count populated notifications)
  const totalResults = await totalCountQuery.lean();
  const totalCount = totalResults.filter(item => item.notification).length;

  // Main query for paginated results
  let query = NotificationRecipient.find(baseQuery)
    .sort({ createdAt: -1 })
    .populate({
      path: 'notification',
      match: { account: filter.account, deletedAt: null },
      populate: [
        { path: 'sender', select: 'firstName lastName email' },
        { path: 'account', select: 'name' },
      ],
      select: { __v: 0, updatedAt: 0, createdAt: 0, updatedBy: 0, createdBy: 0, deletedAt: 0 },
    })
    .select({ updatedAt: 0, __v: 0, deletedAt: 0, recipient: 0 });

  if (page && perPage) {
    const pageNum = parseInt(page) || 0;
    const pageSizeNum = parseInt(perPage) || 25;
    query = query.skip(pageNum * pageSizeNum).limit(pageSizeNum);
  }

  const notifications = await query.lean();

  // Filter out notifications where populate didn't match
  const filteredNotifications = notifications.filter(item => item.notification);

  return {
    data: filteredNotifications,
    totalCount: totalCount,
  };
};

/**
 *
 * @param {string} notificationRecipientId
 * @param {Object} updateData
 * @returns
 */
exports.updateNotificationRecipientById = async (notificationRecipientId, updateData) => {
  return NotificationRecipient.findByIdAndUpdate(notificationRecipientId, updateData);
};

exports.updateNotificationRecipients = async (filter, updateData) => {
  return NotificationRecipient.updateMany(filter, updateData);
};

/**
 * Find users with certificate approval permissions using the database query sequence:
 *
 * @param {string} accountId - The account ID to search within
 * @returns {Promise<Array>} Array of users with certificate approval permissions
 */
exports.getUsersWithCertificateApprovalPermission = async accountId => {
  try {
    // Step 1: Find certificate approval permission
    const certificateApprovalPermission = await Permission.findOne({
      name: 'Certificate Approval',
    });

    if (!certificateApprovalPermission) {
      logger.warn(constantUtils.CERTIFICATE_APPROVAL_PERMISSION_NOT_FOUND);
      return [];
    }

    // Step 2: Find account licenses for this account and permission
    const accountLicenses = await AccountLicence.find({
      account: accountId,
      permission: certificateApprovalPermission._id,
      isApproved: true, // Only approved licenses
    });

    if (accountLicenses.length === 0) {
      logger.info(
        constantUtils.NO_APPROVED_ACCOUNT_LICENSES_FOUND_FOR_CERTIFICATE_APPROVAL_PERMISSION
      );
      return [];
    }

    const accountLicenseIds = accountLicenses.map(license => license._id);

    // Step 3: Find role agreements with read permission
    const roleAgreements = await RoleAgreement.find({
      account: accountId,
      accountLicence: { $in: accountLicenseIds },
      deletedAt: null,
      isActive: true,
      'agreement.read': true,
    }).populate('role');

    if (roleAgreements.length === 0) {
      logger.info(constantUtils.NO_ACTIVE_ROLE_AGREEMENTS_FOUND_WITH_READ_PERMISSION);
      return [];
    }

    const roleIds = roleAgreements.map(agreement => agreement.role._id);

    // Step 4: Find users with matching roles
    const users = await User.find({
      account: accountId,
      role: { $in: roleIds },
      isActive: true,
      isDeleted: false,
    }).select('_id firstName lastName callingName email fcmTokens role');

    logger.info(constantUtils.FOUND_USERS_WITH_CERTIFICATE_APPROVAL_PERMISSION);

    return users;
  } catch (error) {
    logger.error(constantUtils.ERROR_FINDING_USERS_WITH_CERTIFICATE_APPROVAL_PERMISSION);
    throw error;
  }
};

/**
 * Find engineers (mobile users) for web-to-mobile notifications
 * Engineers are users with mobile access type roles
 *
 * @param {string} accountId - The account ID to search within
 * @returns {Promise<Array>} Array of engineer users
 */
exports.getEngineersForNotification = async userId => {
  try {
    const engineers = await User.find({
      _id: userId,
      isActive: true,
      isDeleted: false,
    })
      .populate({
        path: 'role',
        match: {
          accessType: { $in: ['mobile', 'both'] },
          isActive: true,
          deletedAt: null,
        },
      })
      .select('_id firstName lastName callingName email fcmTokens role');

    // Filter out users where role population failed (no matching role)
    const validEngineers = engineers.filter(user => user.role);

    return validEngineers;
  } catch (error) {
    logger.error(constantUtils.ERROR_FINDING_ENGINEERS_FOR_NOTIFICATION);
    throw error;
  }
};

/**
 * Send web-to-mobile certificate notification
 * When Admin uploads certificate from Web without "Internal" checkbox, notify Engineers on Mobile
 *
 * @param {Object} certificateData - Certificate data
 * @returns {Promise<Object>} Result object
 */
exports.sendWebToMobileNotification = async certificateData => {
  try {
    const engineers = await this.getEngineersForNotification(certificateData.userId);

    // Use the generalized bulk notification method
    return await notificationService.sendBulkNotifications({
      title: constantUtils.CERTIFICATE_UPLOADED_WEB_TO_MOBILE_TITLE,
      body: `${constantUtils.CERTIFICATE_UPLOADED_WEB_TO_MOBILE_BODY}${
        certificateData.certificateName ? ` - ${certificateData.certificateName}` : ''
      }${certificateData.firstName ? ` for ${certificateData.firstName}` : ''}`,
      module: global.constant.NOTIFICATION_DETAILS.module.certificateApproval,
      priority: global.constant.NOTIFICATION_DETAILS.priority.medium,
      accountId: certificateData.accountId,
      senderId: certificateData.senderId,
      recipients: engineers,
    });
  } catch (error) {
    logger.error(constantUtils.ERROR_SENDING_WEB_TO_MOBILE_CERTIFICATE_NOTIFICATION);
    throw error;
  }
};

/**
 * Send mobile-to-web certificate notification
 * When any user uploads certificate from Mobile, notify users with certificate approval permissions
 *
 * @param {Object} certificateData - Certificate data
 * @returns {Promise<Object>} Result object
 */
exports.sendMobileToWebNotification = async certificateData => {
  try {
    const approvers = await this.getUsersWithCertificateApprovalPermission(
      certificateData.accountId
    );

    return await notificationService.sendBulkNotifications({
      title: constantUtils.CERTIFICATE_UPLOADED_MOBILE_TO_WEB_TITLE,
      body: `${constantUtils.CERTIFICATE_UPLOADED_MOBILE_TO_WEB_BODY}${
        certificateData.certificateName ? ` - ${certificateData.certificateName}` : ''
      }${certificateData.userName ? ` by ${certificateData.userName}` : ''}`,
      module: global.constant.NOTIFICATION_DETAILS.module.certificateApproval,
      priority: global.constant.NOTIFICATION_DETAILS.priority.high,
      accountId: certificateData.accountId,
      senderId: certificateData.senderId,
      recipients: approvers,
    });
  } catch (error) {
    logger.error(constantUtils.ERROR_SENDING_MOBILE_TO_WEB_CERTIFICATE_NOTIFICATION);
    throw error;
  }
};
